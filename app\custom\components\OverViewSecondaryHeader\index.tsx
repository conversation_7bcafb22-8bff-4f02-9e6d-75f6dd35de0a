import Decimal from 'decimal.js';
import React, { useEffect, useState } from 'react'
import { EntityWithDetails } from '~/utils/db/entities/entities.db.server';
import { RowWithValues } from '~/utils/db/entities/rows.db.server';
import CircleStepper from '../CircleStepper/CircleStepper';
import clsx from 'clsx';
import { Link } from 'react-router';
import { RowValue } from '@prisma/client';



interface InfoItemProps {
  label: string;
  value: string | React.ReactNode;
}

interface TopbarProps {
  items: { label: string; value: string | React.ReactNode }[];
}

const Topbar: React.FC<TopbarProps> = ({ items }) => {
  const InfoItem: React.FC<InfoItemProps> = ({ label, value }) => {
    console.log(label,  value)
    return (
      <div className="flex flex-col gap-4 w-full max-md:max-w-full ">
        <div className="flex flex-row gap-4 items-baseline">
          <div className="w-48 text-xs text-gray-500">
            <span className="text-xs">{label}</span>
          </div>
          <div className="text-sm font-medium text-neutral-800 break-words flex-1">
            {value}
          </div>
        </div>
      </div>
    );
  };


  return (
    <header className="flex flex-col justify-center py-5 rounded-lg w-full max-w-6xl">
      <div className="w-full max-md:max-w-full">
       <div className={clsx("grid grid-cols-2 gap-2")}>
  {row.values?.map((value) => {
    const property = currentEntity?.properties?.find((p: any) => p.id === value.propertyId && !p.isHidden);
    if (!property) return null;

    const { type, subType, attributes } = transformHeader(property);
    const label = property?.title;
    const isFieldRelationTextEditor =
      attributes?.some((attribute: any) => ["EditorSize", "editor", "EditorLanguage"].includes(attribute.name)) || false;
    const isRichText = type === PropertyType.MEDIA || isFieldRelationTextEditor;
    const dynamicRelationClass = clsx(isRichText ? "col-span-full" : "col-span-1");
    
    if (!["ID", "Models.row.folio", "Created at", "Created by"].includes(label)) {
      return (
        <div key={value.id} className={dynamicRelationClass}>
          <InfoItem
            label={label}
            value={
              <div className={clsx(
                !expandedText[value.id] && "line-clamp-2",
                !isRichText && "break-words"
              )}>
                {getValueByType({ type, subType, rowValue: value, attributes, label })}
              </div>
            }
          />
          {isFieldRelationTextEditor && getTextLength({ type, rowValue: value }) > 150 && (
            <button
              className="text-xs font-bold underline cursor-pointer text-primary whitespace-nowrap mt-1"
              onClick={() => toggleTextExpansion(value.id)}
            >
              {expandedText[value.id] ? t("shared.readLess") : t("shared.readMore")}
            </button>
          )}
        </div>
      );
    }
    return null;
  })}
</div>
      </div>
    </header>
  );
};


const OverViewSecondaryHeader = ({ entity, item, rowData }: { entity: EntityWithDetails; item: RowWithValues; rowData: any }) => {
  const [secondaryHeader, setSecondaryHeader] = useState<{ label: string; value: string | React.ReactNode }[]>([]);

  function getFormattedValue(property: any, val: any): string | React.ReactNode {
    if (!val) return "N/A";

    const naClassName = "!text-foreground !font-medium !text-[14px] pl-[2px]";
    const linkClassName = "inline-block items-center gap-5 text-sm text-primary my-auto self-stretch underline decoration-solid decoration-auto underline-offset-auto";
    if (!val || (val.textValue !== null && val.textValue.trim() === "")) {
      return <span className={naClassName}>N/A</span>;
    }
    // Handle text values with different subtypes
    if (val.textValue !== null) {
      if (property.subtype === "url") {
        return (
          <div className="flex items-center gap-5 text-sm text-primary">
            <Link
              to={val.textValue}
              rel="noreferrer"
              target="_blank"
              className={linkClassName}
            >
              {property.title ? `View ${property.title}` : "View"}
            </Link>
          </div>
        );
      }
      if (property.subtype === "email") {
        return (
          <div className="flex items-center gap-3 text-sm text-primary">
            <a href={`mailto:${val.textValue}`} className={linkClassName}>
              {val.textValue}
            </a>
          </div>
        );
      }

      return <div className="break-words">{val.textValue}</div>;
    }
    if (val.numberValue !== null) return val.numberValue.toString();
    if (val.dateValue !== null) return new Date(val.dateValue).toLocaleDateString();

    if (val.booleanValue === false) return "No";
    if (val.booleanValue === true) return "Yes";

    if (val.multiple.length) {
      return <div className="break-words">{val.multiple.map((m: any) => m.value).join(", ")}</div>;
    }

    return <span className={naClassName}>N/A</span>;
  }

  useEffect(() => {
    function getTitleFromProperty(overviewHeaderPropertyId: string, item: RowWithValues) {
      const val = item.values.find((v) => v.propertyId === overviewHeaderPropertyId);
      const property = entity.properties.find((p) => p.id === overviewHeaderPropertyId);
      return getFormattedValue(property, val);
    }

    const overViewSecondaryHeaderProperties = entity.properties.filter((p) => p.isOverviewSecondaryHeaderProperty);
    const mapped = overViewSecondaryHeaderProperties.map((property) => {
      return {
        label: property.title || property.name,
        value: getTitleFromProperty(property.id, item),
      };
    });
    setSecondaryHeader(mapped);
  }, [entity, item]);

  return (
    (secondaryHeader.length > 0 || rowData?.entity?.isStepFormWizard) && (
      <div className="flex justify-between items-center w-full bg-card">
        {secondaryHeader.length > 0 && <Topbar items={secondaryHeader} />}
      </div>
    )
  );
};

export default OverViewSecondaryHeader;